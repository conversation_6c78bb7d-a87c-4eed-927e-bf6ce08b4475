# Zencoder Plugin

一个为 IntelliJ IDEA 开发的 Zencoder 无认证扩展插件。

## 项目概述

Zencoder Plugin 提供了绕过 OAuth 认证流程的功能，适用于开发和测试环境。插件提供模拟用户凭证，使所有 Zencoder 功能无需登录即可使用。

## 主要功能

- 🔐 **绕过认证**: 自动绕过 OAuth 认证流程
- 👤 **模拟用户**: 提供模拟用户凭证
- 🚀 **离线开发**: 支持无网络环境下的开发
- ⚙️ **配置管理**: 灵活的设置选项
- 🔧 **开发工具**: 专为开发和测试环境设计
- ⚠️ **安全提醒**: 仅限开发测试使用

## 技术栈

- **开发语言**: Kotlin + Java 17+
- **构建工具**: Gradle
- **IDE平台**: IntelliJ Platform 2024.2.4
- **依赖管理**: Gradle + IntelliJ Platform Plugin
- **网络库**: OkHttp 4.11.0

## 项目结构

```
zencoder-plugin/
├── src/main/java/ai/zencoder/plugin/
│   ├── auth/                       # 认证相关模型
│   │   ├── AuthInfo.java          # 认证信息模型
│   │   └── UserData.java          # 用户数据模型
│   ├── bypass/                     # 核心绕过组件
│   │   ├── ZencoderReflectionUtils.java      # 反射工具类 (已优化)
│   │   ├── ZencoderCredentialInjector.java   # 凭证注入器
│   │   ├── ZencoderServiceHijacker.java      # 服务劫持器
│   │   ├── ZencoderTargetDiscovery.java      # 目标发现
│   │   ├── ZencoderStateSynchronizer.java    # 状态同步器
│   │   └── ZencoderBypassInitializer.java    # 初始化器
│   └── config/                     # 配置相关
│       └── ZencoderConfig.java    # 配置管理
├── src/main/resources/META-INF/
│   └── plugin.xml                  # 插件配置
├── build.gradle                    # 构建配置
├── gradle.properties              # Gradle属性
├── settings.gradle                # 项目设置
└── README.md                      # 项目说明
```

## 快速开始

### 环境要求

- **JDK**: 17 或更高版本
- **IntelliJ IDEA**: 2024.2.4 或更高版本
- **Gradle**: 项目自带wrapper

### 构建插件

```bash
# 清理之前的构建
./gradlew clean

# 构建插件
./gradlew buildPlugin

# 生成的插件位于: build/distributions/zencoder-plugin-1.0.0.zip
```

### 安装插件

1. 打开 IntelliJ IDEA
2. 进入 `File` → `Settings` → `Plugins`
3. 点击齿轮图标 → `Install Plugin from Disk...`
4. 选择 `build/distributions/zencoder-plugin-1.0.0.zip`
5. 重启 IntelliJ IDEA

## 开发指南

### 开发环境设置

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd zencoder-plugin
   ```

2. **导入到IntelliJ IDEA**
   - 打开 IntelliJ IDEA
   - 选择 `Open` 并选择项目目录
   - 等待Gradle同步完成

3. **运行开发环境**
   ```bash
   ./gradlew runIde
   ```

### 主要组件说明

#### 1. 核心绕过组件
- `ZencoderReflectionUtils`: 反射工具类 (已优化，支持TTL缓存)
- `ZencoderCredentialInjector`: 凭证注入器，注入虚假认证信息
- `ZencoderServiceHijacker`: 服务劫持器，替换官方认证服务
- `ZencoderTargetDiscovery`: 目标发现，自动发现需要绕过的目标类
- `ZencoderStateSynchronizer`: 状态同步器，管理绕过状态
- `ZencoderBypassInitializer`: 初始化器，系统启动时初始化绕过机制

#### 2. 认证模型
- `AuthInfo`: 认证信息模型，包含访问令牌和刷新令牌
- `UserData`: 用户数据模型，存储用户相关信息

#### 3. 配置管理
- `ZencoderConfig`: 配置管理类
- 通过 IntelliJ Platform 的配置系统管理设置
- 支持项目级别的配置选项

### 构建配置

项目使用 IntelliJ Platform Gradle Plugin 进行构建：

- **插件版本**: 2.5.0
- **目标平台**: IntelliJ IDEA Community 2024.2.4
- **兼容性**: 233 - 243.*

### 依赖项

- `org.jetbrains.kotlin:kotlin-stdlib` - Kotlin 标准库
- `com.squareup.okhttp3:okhttp:4.11.0` - HTTP 客户端

## 测试

```bash
# 运行测试
./gradlew test

# 运行插件验证
./gradlew verifyPlugin
```

## 发布

### 版本管理

版本号在以下文件中定义：
- `build.gradle` - Gradle构建版本
- `src/main/resources/META-INF/plugin.xml` - 插件版本

### 发布流程

1. 更新版本号
2. 运行构建脚本
3. 测试生成的插件
4. 发布到插件市场或内部分发

## 使用说明

### 功能特性

1. **自动绕过认证**: 插件启动后自动绕过 Zencoder 的 OAuth 认证流程
2. **模拟用户状态**: 提供模拟的用户凭证和状态信息
3. **开发友好**: 专为开发和测试环境设计，无需真实的用户账户
4. **配置灵活**: 支持通过设置页面进行配置调整

### 注意事项

⚠️ **重要提醒**: 此插件仅用于开发和测试目的，请勿在生产环境中使用。

## 故障排除

### 常见问题

1. **构建失败**
   - 检查JDK版本 (需要17+)
   - 确认网络连接正常
   - 清理Gradle缓存: `./gradlew clean`

2. **插件无法加载**
   - 确认IntelliJ IDEA版本兼容性 (需要 233+)
   - 检查插件文件完整性
   - 确认 Zencoder 主插件已安装

3. **无认证模式未生效**
   - 检查插件是否正确启用
   - 重启 IntelliJ IDEA
   - 查看 IDE 日志获取详细错误信息

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 创建 Pull Request

## 许可证

本项目采用 [许可证名称] 许可证。

## 联系方式

- **开发商**: Zencoder Team
- **邮箱**: <EMAIL>
- **网站**: https://zencoder.ai

## 更新日志

### v1.0.0 (2025-08-29) - 优化版本
- 🎉 初始发布版本
- ✨ 绕过 OAuth 认证流程
- ✨ 提供模拟用户凭证
- ✨ 支持离线开发模式
- ✨ 配置管理界面
- ✨ 开发工具集成
- 🚀 **性能优化**: 优化反射工具类，添加智能缓存机制
- 🧹 **代码清理**: 删除冗余代码和失败的测试
- ⚡ **缓存增强**: 添加TTL支持和自动过期清理
- 🔧 **构建优化**: 修复构建环境问题，确保项目正常编译
- 📦 **分发包**: 生成完整的插件分发包 `zencoder-plugin-1.0.0.zip`
