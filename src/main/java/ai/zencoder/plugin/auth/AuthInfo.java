package ai.zencoder.plugin.auth;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.Objects;

/**
 * 认证信息类
 * 包含访问令牌和刷新令牌
 */
public class AuthInfo {
    
    @NotNull
    private final String accessToken;
    
    @Nullable
    private final String refreshToken;
    
    private final long expiresAt;
    
    public AuthInfo(@NotNull String accessToken, @Nullable String refreshToken) {
        this(accessToken, refreshToken, System.currentTimeMillis() + 3600000); // 默认1小时过期
    }
    
    public AuthInfo(@NotNull String accessToken, @Nullable String refreshToken, long expiresAt) {
        this.accessToken = Objects.requireNonNull(accessToken, "accessToken cannot be null");
        this.refreshToken = refreshToken;
        this.expiresAt = expiresAt;
    }
    
    @NotNull
    public String getAccessToken() {
        return accessToken;
    }
    
    @Nullable
    public String getRefreshToken() {
        return refreshToken;
    }
    
    public long getExpiresAt() {
        return expiresAt;
    }
    
    /**
     * 检查访问令牌是否已过期
     * @return true如果已过期，false如果仍然有效
     */
    public boolean isExpired() {
        return System.currentTimeMillis() >= expiresAt;
    }
    
    /**
     * 检查访问令牌是否即将过期（5分钟内）
     * @return true如果即将过期，false如果还有足够时间
     */
    public boolean isExpiringSoon() {
        return System.currentTimeMillis() >= (expiresAt - 300000); // 5分钟
    }
    
    @Override
    public String toString() {
        return "AuthInfo{" +
                "accessToken='" + (accessToken.length() > 10 ? accessToken.substring(0, 10) + "..." : accessToken) + '\'' +
                ", refreshToken=" + (refreshToken != null ? "present" : "null") +
                ", expiresAt=" + expiresAt +
                ", isExpired=" + isExpired() +
                '}';
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        
        AuthInfo authInfo = (AuthInfo) o;
        
        if (expiresAt != authInfo.expiresAt) return false;
        if (!accessToken.equals(authInfo.accessToken)) return false;
        return refreshToken != null ? refreshToken.equals(authInfo.refreshToken) : authInfo.refreshToken == null;
    }
    
    @Override
    public int hashCode() {
        int result = accessToken.hashCode();
        result = 31 * result + (refreshToken != null ? refreshToken.hashCode() : 0);
        result = 31 * result + (int) (expiresAt ^ (expiresAt >>> 32));
        return result;
    }
}
